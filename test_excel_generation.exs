# Minimal test for Excel generation issue
# This tests the core Excel generation without the full application context

# Test basic elixlsx functionality
alias Elixlsx.{Workbook, Sheet}

# Create a simple test workbook
test_headers = ["Date", "ID", "Description", "Amount"]
test_data = [
  ["2024-01-15", "TEST001", "Test Transaction", "100.50"],
  ["2024-01-16", "TEST002", "Another Test", "250.75"]
]

all_rows = [test_headers | test_data]

# Create column widths map (like the working code)
col_widths = %{
  0 => 15,  # Date
  1 => 20,  # ID  
  2 => 40,  # Description
  3 => 15   # Amount
}

workbook = %Workbook{
  sheets: [
    %Sheet{
      name: "Test",
      rows: all_rows,
      col_widths: col_widths
    }
  ]
}

IO.puts("🧪 Testing Excel Generation")
IO.puts("=" |> String.duplicate(40))

try do
  # Test Excel generation
  IO.puts("Creating workbook with #{length(all_rows)} rows...")
  
  excel_result = Elixlsx.write_to_memory(workbook, "test.xlsx")
  result_type = try do
    excel_result |> elem(0)
  rescue
    _ -> :error
  end
  IO.puts("Excel generation result type: #{inspect(result_type)}")
  
  # Handle the return format
  binary_data = case excel_result do
    {:ok, {_filename, data}} -> 
      IO.puts("Got Excel data with filename, size: #{byte_size(data)} bytes")
      data
    {:ok, data} -> 
      IO.puts("Got Excel data directly, size: #{byte_size(data)} bytes")
      data
    data when is_binary(data) -> 
      IO.puts("Got binary data directly, size: #{byte_size(data)} bytes")
      data
    error -> 
      IO.puts("Failed to generate Excel binary: #{inspect(error)}")
      raise "Failed to generate Excel file: #{inspect(error)}"
  end
  
  # Save to file
  output_path = "test_output.xlsx"
  File.write!(output_path, binary_data)
  
  # Verify file
  if File.exists?(output_path) do
    size = File.stat!(output_path).size
    IO.puts("✅ File saved successfully!")
    IO.puts("   Path: #{output_path}")
    IO.puts("   Size: #{size} bytes")
    
    if size > 0 do
      # Check if it's a valid Excel file
      {:ok, first_bytes} = File.read(output_path, 4)
      if String.starts_with?(first_bytes, "PK") do
        IO.puts("   ✓ File appears to be a valid Excel file (ZIP signature)")
      else
        IO.puts("   ⚠️  File may be corrupted (no ZIP signature)")
        IO.puts("   First 4 bytes: #{inspect(first_bytes)}")
      end
    else
      IO.puts("   ❌ File is empty")
    end
  else
    IO.puts("   ❌ File was not created")
  end
  
rescue
  error ->
    IO.puts("❌ Error: #{Exception.message(error)}")
    IO.puts("   #{inspect(error)}")
end

IO.puts("\n🎯 Test completed!")
