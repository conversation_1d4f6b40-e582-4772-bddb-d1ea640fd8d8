# Demo script for xlsxir-based Excel export
# Run with: mix run scripts/demo_xlsxir_export.exs

# Start the application to ensure all dependencies are loaded
Application.ensure_all_started(:reconciliation)

alias Reconciliation.Services.ExcelExport

# Sample transaction data for demonstration
sample_transactions = [
  %{
    transaction_date: ~D[2024-01-15],
    transaction_id: "TXN001",
    description: "Sample Transaction 1",
    reference: "REF001",
    amount: Decimal.new("100.50"),
    transaction_type: "debit",
    account: "ACC001",
    category: "Transfer",
    currency: "USD",
    is_matched: true,
    match_confidence: Decimal.new("95.5"),
    uploaded_file: %{
      filename: "sample_file_a.csv",
      file_type: "file_a"
    }
  },
  %{
    transaction_date: ~D[2024-01-16],
    transaction_id: "TXN002",
    description: "Sample Transaction 2",
    reference: "REF002",
    amount: Decimal.new("250.75"),
    transaction_type: "credit",
    account: "ACC002",
    category: "Payment",
    currency: "USD",
    is_matched: false,
    match_confidence: nil,
    uploaded_file: %{
      filename: "sample_file_b.csv",
      file_type: "file_b"
    }
  },
  %{
    transaction_date: ~D[2024-01-17],
    transaction_id: "TXN003",
    description: "Sample Transaction 3",
    reference: "REF003",
    amount: Decimal.new("75.25"),
    transaction_type: "debit",
    account: "ACC001",
    category: "Fee",
    currency: "EUR",
    is_matched: true,
    match_confidence: Decimal.new("88.2"),
    uploaded_file: %{
      filename: "sample_file_a.csv",
      file_type: "file_a"
    }
  }
]

IO.puts("🚀 Xlsxir Excel Export Demo")
IO.puts("=" |> String.duplicate(50))

# Demo 1: Analyze template (will fail gracefully if template doesn't exist)
IO.puts("\n📊 Demo 1: Analyzing template structure")
case ExcelExport.analyze_template() do
  {:ok, analysis} ->
    IO.puts("✅ Template analysis successful!")
    IO.puts("   Headers: #{inspect(analysis.headers)}")
    IO.puts("   Data starts at row: #{analysis.data_start_row}")
    IO.puts("   Total rows in template: #{analysis.total_rows}")
    IO.puts("   Template path: #{analysis.template_path}")
  
  {:error, reason} ->
    IO.puts("⚠️  Template analysis failed (expected if no template exists)")
    IO.puts("   Reason: #{reason}")
end

# Demo 2: Generate Excel using xlsxir approach
IO.puts("\n📁 Demo 2: Generating Excel with xlsxir approach")
case ExcelExport.generate_excel_with_xlsxir(sample_transactions, "demo_export") do
  {:ok, file_path, filename} ->
    IO.puts("✅ Excel generation successful!")
    IO.puts("   File: #{filename}")
    IO.puts("   Path: #{file_path}")
    IO.puts("   Size: #{File.stat!(file_path).size} bytes")
    IO.puts("   Transactions exported: #{length(sample_transactions)}")
    
    # Show file exists
    if File.exists?(file_path) do
      IO.puts("   ✓ File exists and is accessible")
    end
  
  {:error, reason} ->
    IO.puts("❌ Excel generation failed")
    IO.puts("   Reason: #{reason}")
end

# Demo 3: Try with custom template (will fallback gracefully)
IO.puts("\n🎨 Demo 3: Trying custom template export")
case ExcelExport.export_with_custom_template(sample_transactions, "custom_template.xlsx", "custom_demo") do
  {:ok, file_path, filename} ->
    IO.puts("✅ Custom template export successful!")
    IO.puts("   File: #{filename}")
    IO.puts("   Path: #{file_path}")
  
  {:error, reason} ->
    IO.puts("⚠️  Custom template export failed (expected if template doesn't exist)")
    IO.puts("   Reason: #{reason}")
end

IO.puts("\n🎯 Demo completed!")
IO.puts("=" |> String.duplicate(50))

# Show available functions
IO.puts("\n📚 Available xlsxir export functions:")
IO.puts("   • ExcelExport.generate_excel_with_xlsxir/3")
IO.puts("   • ExcelExport.export_with_custom_template/3")
IO.puts("   • ExcelExport.analyze_template/1")

IO.puts("\n💡 Usage tips:")
IO.puts("   • Place Excel templates in project root directory")
IO.puts("   • Templates should have headers in first few rows")
IO.puts("   • System auto-detects headers containing 'date', 'amount', 'transaction', etc.")
IO.puts("   • Falls back to programmatic generation if template missing")
IO.puts("   • Check completed/ folder for generated files")

# List files in completed folder if it exists
completed_dir = Path.join(File.cwd!(), "completed")
if File.exists?(completed_dir) do
  files = File.ls!(completed_dir)
  if length(files) > 0 do
    IO.puts("\n📂 Files in completed/ folder:")
    Enum.each(files, fn file ->
      file_path = Path.join(completed_dir, file)
      stat = File.stat!(file_path)
      IO.puts("   • #{file} (#{stat.size} bytes, #{stat.mtime})")
    end)
  else
    IO.puts("\n📂 completed/ folder is empty")
  end
else
  IO.puts("\n📂 completed/ folder doesn't exist yet")
end
