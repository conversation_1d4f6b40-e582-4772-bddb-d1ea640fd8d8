# Simple test script for xlsxir Excel export
# Run with: mix run scripts/test_xlsxir_simple.exs

# Start the application to ensure all dependencies are loaded
Application.ensure_all_started(:reconciliation)

alias Reconciliation.Services.ExcelExport

# Create a very simple test transaction
test_transaction = %{
  transaction_date: ~D[2024-01-15],
  transaction_id: "TEST001",
  description: "Test Transaction",
  reference: "REF001",
  amount: Decimal.new("100.50"),
  transaction_type: "debit",
  account: "ACC001",
  category: "Test",
  currency: "USD",
  is_matched: true,
  match_confidence: Decimal.new("95.5"),
  uploaded_file: %{
    filename: "test.csv",
    file_type: "file_a"
  }
}

IO.puts("🧪 Simple Xlsxir Export Test")
IO.puts("=" |> String.duplicate(40))

# Test 1: Generate Excel without template (should fallback to programmatic generation)
IO.puts("\n📊 Test 1: Generate Excel without template")
case ExcelExport.generate_excel_with_xlsxir([test_transaction], "simple_test") do
  {:ok, file_path, filename} ->
    IO.puts("✅ Success!")
    IO.puts("   File: #{filename}")
    IO.puts("   Path: #{file_path}")
    
    # Check file size
    if File.exists?(file_path) do
      size = File.stat!(file_path).size
      IO.puts("   Size: #{size} bytes")
      
      if size > 0 do
        IO.puts("   ✓ File has content")
        
        # Try to read first few bytes to check if it's a valid Excel file
        {:ok, first_bytes} = File.read(file_path, 4)
        if String.starts_with?(first_bytes, "PK") do
          IO.puts("   ✓ File appears to be a valid ZIP/Excel file (starts with PK)")
        else
          IO.puts("   ⚠️  File may be corrupted (doesn't start with PK signature)")
          IO.puts("   First 4 bytes: #{inspect(first_bytes)}")
        end
      else
        IO.puts("   ❌ File is empty")
      end
    else
      IO.puts("   ❌ File doesn't exist")
    end
    
  {:error, reason} ->
    IO.puts("❌ Failed: #{reason}")
end

# Test 2: Test with existing Excel generation method for comparison
IO.puts("\n📊 Test 2: Generate Excel with existing method (for comparison)")
case ExcelExport.generate_transactions_excel([test_transaction]) do
  {:ok, file_path, filename} ->
    IO.puts("✅ Existing method success!")
    IO.puts("   File: #{filename}")
    IO.puts("   Path: #{file_path}")
    
    if File.exists?(file_path) do
      size = File.stat!(file_path).size
      IO.puts("   Size: #{size} bytes")
      
      {:ok, first_bytes} = File.read(file_path, 4)
      if String.starts_with?(first_bytes, "PK") do
        IO.puts("   ✓ Existing method produces valid Excel file")
      else
        IO.puts("   ⚠️  Existing method also has issues")
      end
    end
    
  {:error, reason} ->
    IO.puts("❌ Existing method failed: #{reason}")
end

IO.puts("\n🎯 Test completed!")
