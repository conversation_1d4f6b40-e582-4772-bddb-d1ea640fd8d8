# Xlsxir-based Excel Export Usage Guide

This document explains how to use the new xlsxir-based Excel export functionality in the Reconciliation system.

## Overview

The xlsxir-based export system provides more control over template reading and data population by:

1. **Reading Excel templates** using xlsxir to understand structure
2. **Extracting headers and layout** from existing templates
3. **Populating data** using elixlsx for reliable Excel generation
4. **Maintaining flexibility** for custom template formats

## Key Functions

### `generate_excel_with_xlsxir/3`

Main function for generating Excel exports using xlsxir template reading.

```elixir
# Basic usage
{:ok, file_path, filename} = ExcelExport.generate_excel_with_xlsxir(transactions, "my_export")

# With custom template
{:ok, file_path, filename} = ExcelExport.generate_excel_with_xlsxir(
  transactions, 
  "my_export", 
  "custom_template.xlsx"
)
```

### `export_with_custom_template/3`

Convenience function for using custom templates.

```elixir
{:ok, file_path, filename} = ExcelExport.export_with_custom_template(
  transactions,
  "my_custom_template.xlsx",
  "export_name"
)
```

### `analyze_template/1`

Analyze template structure without generating export.

```elixir
{:ok, analysis} = ExcelExport.analyze_template("my_template.xlsx")

# Returns:
# %{
#   headers: ["Date", "Transaction ID", "Description", ...],
#   data_start_row: 2,
#   sheet_info: %{...},
#   total_rows: 10,
#   template_path: "/path/to/template.xlsx"
# }
```

## Template Requirements

### Header Detection

The system automatically detects headers by looking for rows containing:
- "date", "transaction_date", "transaction date"
- "amount"
- "transaction", "transaction_id"
- "reference"
- "description"

### Supported Column Headers

The system maps transaction fields to columns based on header names (case-insensitive):

| Transaction Field | Supported Headers |
|------------------|-------------------|
| transaction_date | "date", "transaction_date", "transaction date" |
| transaction_id | "transaction_id", "transaction id", "id" |
| description | "description", "desc" |
| reference | "reference", "ref", "reference_number" |
| amount | "amount" |
| transaction_type | "type", "transaction_type", "transaction type" |
| account | "account", "account_number" |
| category | "category" |
| currency | "currency" |
| is_matched | "matched", "is_matched" |
| match_confidence | "confidence", "match_confidence" |
| source_file | "source_file", "source file", "file" |
| file_type | "file_type", "file type" |

## Usage in LiveView

### Adding Xlsxir Export Button

```elixir
# In your LiveView template
<button
  phx-click="export_transactions_xlsxir"
  phx-value-run_id={@selected_run_id}
  class="px-4 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
>
  Export with Xlsxir
</button>
```

### Handling Export Event

```elixir
def handle_event("export_transactions_xlsxir", %{"run_id" => run_id}, socket) do
  # Get transactions
  transactions = get_filtered_transactions(run_id)
  run = get_reconciliation_run!(run_id)
  
  case ExcelExport.generate_excel_with_xlsxir(transactions, run.name) do
    {:ok, file_path, filename} ->
      file_content = File.read!(file_path)
      
      {:noreply,
       socket
       |> push_event("download", %{
         url: "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,#{Base.encode64(file_content)}",
         filename: filename
       })
       |> put_flash(:info, "Excel exported using xlsxir!")
      }
      
    {:error, reason} ->
      {:noreply,
       socket
       |> put_flash(:error, "Export failed: #{reason}")
      }
  end
end
```

## Error Handling

The system includes comprehensive error handling:

1. **Missing Template**: Falls back to programmatic generation
2. **Invalid Template**: Returns descriptive error messages
3. **Data Population Errors**: Logs errors and provides fallback
4. **File System Errors**: Handles permissions and disk space issues

## Benefits of Xlsxir Approach

1. **Template Flexibility**: Read any Excel template structure
2. **Header Intelligence**: Automatically detect and map headers
3. **Fallback Support**: Graceful degradation when templates are missing
4. **Better Control**: More precise control over data mapping
5. **Debugging**: Template analysis for troubleshooting

## Example Template Structure

Create an Excel file with headers in the first few rows:

```
| Date | Transaction ID | Description | Reference | Amount | Type | Account | Currency | Matched |
|------|----------------|-------------|-----------|--------|------|---------|----------|---------|
|      |                |             |           |        |      |         |          |         |
```

The system will:
1. Detect these headers automatically
2. Map transaction data to appropriate columns
3. Generate a populated Excel file
4. Preserve the general structure and column widths

## Testing

Run the xlsxir export tests:

```bash
mix test test/services/excel_export_xlsxir_test.exs
```

## Troubleshooting

### Template Not Found
- Ensure template file exists in project root
- Check file permissions
- Use `analyze_template/1` to verify template structure

### Header Mapping Issues
- Use `analyze_template/1` to see detected headers
- Ensure headers contain expected keywords
- Check case sensitivity (system is case-insensitive)

### Export Failures
- Check logs for detailed error messages
- Verify transaction data completeness
- Ensure sufficient disk space in completed/ folder
